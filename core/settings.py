

import environ
import os
import sentry_sdk
import resend
import cloudinary
import cloudinary.uploader
from pathlib import Path
from datetime import timedelta
from dotenv import load_dotenv


load_dotenv(override=True)


env = environ.Env(
    # set casting, default value
    DEBUG=(bool, False),

    # CORS
    CORS_ORIGIN_ALLOW_ALL=(bool, True),
    CORS_ALLOW_CREDENTIALS=(bool, True),
    CORS_ALLOWED_ORIGINS=(list, []),

    #Paystack
    PAYSTACK_SECRET_KEY = (str, ""),
    PAYSTACK_API_KEY = (str, ""),
    PAYSTACK_BASE_URL = (str, "https://api.paystack.co"),

    # Coludinary
    CLOUDINARY_CLOUD_NAME=(str, None),
    CLOUDINARY_API_KEY=(str, None),
    CLOUDINARY_API_SECRET=(str, None),

    SENTRY_DSN=(str, None),

    OTP_EXPIRY_MINUTES=(str, 10),
    OTP_CODE_LEN = (int, 4),

    RESEND_API_KEY=(str, None),
)

sentry_sdk.init(
    dsn=env('SENTRY_DSN'),
    send_default_pii=True,
)


cloudinary.config(
    cloud_name=env('CLOUDINARY_CLOUD_NAME'),
    api_key=env('CLOUDINARY_API_KEY'),
    api_secret=env('CLOUDINARY_API_SECRET'),
    secure=True,
)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

environ.Env.read_env(os.path.join(BASE_DIR, '.env'), overwrite=True)

SECRET_KEY = env('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG')

# Default auth model
AUTH_USER_MODEL = 'authentication.User'

ALLOWED_HOSTS = [
    *env.list('ALLOWED_HOSTS')
]

CSRF_TRUSTED_ORIGINS = [
    *env.list('CSRF_TRUSTED_ORIGINS')
]


CORS_ORIGIN_ALLOW_ALL = env('CORS_ORIGIN_ALLOW_ALL')
CORS_ALLOW_CREDENTIALS = env('CORS_ALLOW_CREDENTIALS')
CORS_ALLOWED_ORIGINS = env('CORS_ALLOWED_ORIGINS')

PAYSTACK_SECRET_KEY = env("PAYSTACK_SECRET_KEY")  
PAYSTACK_API_KEY = env("PAYSTACK_API_KEY")
PAYSTACK_BASE_URL=env("PAYSTACK_BASE_URL")

resend.api_key = env("RESEND_API_KEY")

OTP_EXPIRY_MINUTES = env('OTP_EXPIRY_MINUTES')
OTP_CODE_LEN = env('OTP_CODE_LEN')

PHONENUMBER_DEFAULT_REGION='NG'


SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=6),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "SIGNING_KEY": SECRET_KEY,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "TOKEN_OBTAIN_SERIALIZER": "authentication.serializers.CustomTokenObtainPairSerializer",
}

SPECTACULAR_SETTINGS = {
    'TITLE': "Macky's Food API",
    'DESCRIPTION': "Macky's Food API Documentation",
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    # OTHER SETTINGS
}


REST_FRAMEWORK = {
    'DEFAULT_SCHEMA_CLASS': 'core.schemas.CustomAutoSchema', 
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'core.parsers.NestedMultiPartParser',
        'rest_framework.parsers.FormParser',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'anon': '100/minute',
    'user': '100/minute',
    'DEFAULT_AUTHENTICATION_CLASSES': ['rest_framework_simplejwt.authentication.JWTAuthentication'],
    'AUTHENTICATION_BACKENDS': ['authentication.auth_backends.EmailOrPhoneBackend'],
    'DEFAULT_PERMISSION_CLASSES': ['rest_framework.permissions.IsAuthenticated'],
    'DEFAULT_RENDERER_CLASSES': ['core.renderers.CustomRenderer'],
    'DEFAULT_FILTER_BACKENsDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 20,
}


STORAGES = {
    'default': {
        "BACKEND": 'cloudinary_storage.storage.MediaCloudinaryStorage',
    },
    "staticfiles": {
        "BACKEND": 'whitenoise.storage.CompressedManifestStaticFilesStorage',
    },
}


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    'rest_framework',
    'corsheaders',
    'drf_spectacular',
    "phonenumber_field",

    'authentication.apps.AuthenticationConfig',
    'user_profiles.apps.UserProfilesConfig',
    'foods.apps.FoodsConfig',
    'wallet.apps.WalletConfig',
    'order.apps.OrderConfig',
    'rider.apps.RiderConfig',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'core.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'core.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    'default': env.db(),
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
