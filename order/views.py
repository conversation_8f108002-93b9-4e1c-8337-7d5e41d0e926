from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db import transaction
from .models import FoodTray, FoodTrayItem, DeliveryAddress, Order, OrderTracking
from .serializers import (
    FoodTraySerializer, FoodTrayItemSerializer, AddToCartSerializer,
    UpdateCartItemSerializer, DeliveryAddressSerializer, CheckoutSerializer,
    PaymentSerializer, OrderSerializer, OrderTrackingSerializer
)


class CartView(generics.RetrieveAPIView):
    serializer_class = FoodTraySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        food_tray, created = FoodTray.objects.get_or_create(
            user=self.request.user,
            status='open',
            defaults={'user': self.request.user}
        )
        return food_tray


class AddToCartView(generics.CreateAPIView):
    serializer_class = AddToCartSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        food_tray_item = serializer.save()

        food_tray = food_tray_item.food_tray
        cart_serializer = FoodTraySerializer(food_tray)

        return Response({
            'message': 'Item added to cart successfully',
            'cart': cart_serializer.data
        }, status=status.HTTP_201_CREATED)


class UpdateCartItemView(generics.UpdateAPIView):
    # Update quantity of item in cart or remove if quantity is 0
    serializer_class = UpdateCartItemSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        item_id = self.kwargs['item_id']
        return get_object_or_404(
            FoodTrayItem,
            id=item_id,
            food_tray__user=self.request.user,
            food_tray__status='open'
        )

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)

        updated_item = serializer.save()

        if updated_item is None:
            message = 'Item removed from cart successfully'
        else:
            message = 'Item quantity updated successfully'

        # Return the updated cart
        food_tray = instance.food_tray
        cart_serializer = FoodTraySerializer(food_tray)

        return Response({
            'message': message,
            'cart': cart_serializer.data
        })


class RemoveFromCartView(generics.DestroyAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        item_id = self.kwargs['item_id']
        return get_object_or_404(
            FoodTrayItem,
            id=item_id,
            food_tray__user=self.request.user,
            food_tray__status='open'
        )

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        food_tray = instance.food_tray
        instance.delete()

        cart_serializer = FoodTraySerializer(food_tray)

        return Response({
            'message': 'Item removed from cart successfully',
            'cart': cart_serializer.data
        })


class ClearCartView(generics.GenericAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def delete(self, request):
        try:
            food_tray = FoodTray.objects.get(user=request.user, status='open')
            food_tray.foodtrayitem_set.all().delete()

            cart_serializer = FoodTraySerializer(food_tray)
            return Response({
                'message': 'Cart cleared successfully',
                'cart': cart_serializer.data
            })
        except FoodTray.DoesNotExist:
            return Response({
                'message': 'No active cart found'
            }, status=status.HTTP_404_NOT_FOUND)


class DeliveryAddressListCreateView(generics.ListCreateAPIView):
    serializer_class = DeliveryAddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return DeliveryAddress.objects.filter(user=self.request.user).order_by('-created_at')


class DeliveryAddressDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = DeliveryAddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return DeliveryAddress.objects.filter(user=self.request.user)


class CheckoutView(generics.CreateAPIView):
    serializer_class = CheckoutSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        result = serializer.save()
        checkout_bill = result['checkout_bill']

        return Response({
            'message': 'Checkout completed successfully',
            'total_amount': checkout_bill.total,
            'payment_required': True
        }, status=status.HTTP_201_CREATED)


class PaymentView(generics.CreateAPIView):
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        result = serializer.save()

        return Response({
            'message': 'Payment processed successfully',
            'order_id': result['order'].id,
            'tracking_id': result['tracking_id'],
            'payment_status': result['payment_status'],
            'amount_paid': result['amount_paid']
        }, status=status.HTTP_200_OK)


# Order Management Views
class OrderListView(generics.ListAPIView):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user).order_by('-created_at')


class OrderDetailView(generics.RetrieveAPIView):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)


class OrderTrackingView(generics.RetrieveAPIView):
    serializer_class = OrderTrackingSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'tracking_id'

    def get_queryset(self):
        return OrderTracking.objects.filter(order__user=self.request.user)


class OrderTrackingByOrderView(generics.RetrieveAPIView):
    serializer_class = OrderTrackingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        order_id = self.kwargs['order_id']
        return get_object_or_404(
            OrderTracking,
            order__id=order_id,
            order__user=self.request.user
        )


