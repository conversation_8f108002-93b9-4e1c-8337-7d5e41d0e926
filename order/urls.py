from django.urls import path
from . import views

urlpatterns = [
    # Cart Management URLs
    path('cart/', views.CartView.as_view(), name='cart'),
    path('cart/add/', views.AddToCartView.as_view(), name='add-to-cart'),
    path('cart/update/<uuid:item_id>/', views.UpdateCartItemView.as_view(), name='update-cart-item'),
    path('cart/remove/<uuid:item_id>/', views.RemoveFromCartView.as_view(), name='remove-from-cart'),
    path('cart/clear/', views.ClearCartView.as_view(), name='clear-cart'),
    
    # Delivery Address URLs
    path('delivery-addresses/', views.DeliveryAddressListCreateView.as_view(), name='delivery-addresses'),
    path('delivery-addresses/<uuid:pk>/', views.DeliveryAddressDetailView.as_view(), name='delivery-address-detail'),
    
    # Checkout and Payment URLs
    path('checkout/', views.CheckoutView.as_view(), name='checkout'),
    path('payment/', views.PaymentView.as_view(), name='payment'),
    
    # Order Management URLs
    path('orders/', views.OrderListView.as_view(), name='orders'),
    path('orders/<uuid:pk>/', views.OrderDetailView.as_view(), name='order-detail'),
    
    # Order Tracking URLs
    path('tracking/<str:tracking_id>/', views.OrderTrackingView.as_view(), name='order-tracking'),
    path('orders/<uuid:order_id>/tracking/', views.OrderTrackingByOrderView.as_view(), name='order-tracking-by-order'),
    
    # Wallet URLs
]
