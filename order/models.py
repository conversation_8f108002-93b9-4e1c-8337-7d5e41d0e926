from uuid import uuid4

from django.db import models
from authentication.models import User
from foods.models import Food
from rider.models import DispatchDriver


class FoodTrayStatusChoices(models.TextChoices):
    OPEN = ("open", "Open")
    CLOSED = ("closed", "Closed")


class OrderStatusChoices(models.TextChoices):
    DELIVERED = ("delivered", "Delivered")
    PROCESSING = ("processing", "Processing")
    CANCELLED = ("cancelled", "Cancelled")


class PaymentModeChoices(models.TextChoices):
    WALLET = ("wallet", "Wallet")
    BANK_TRANSFER = ("bank_transfer", "Bank Transfer")


class OrderTrackingStatusChoices(models.TextChoices):
    CONFIRMED = ("confirmed", "Confirmed")
    PACKING = ("packed", "Packed")
    EN_ROUTE = ("en_route", "EN ROUTE")
    DELIVERED = ("delivered", "Delivered")


class FoodTray(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(max_length=50, choices=FoodTrayStatusChoices.choices, default="open")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id}: {self.user}-{self.status}"


class FoodTrayItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    food_tray = models.ForeignKey(FoodTray, on_delete=models.CASCADE)
    food = models.ForeignKey(Food, on_delete=models.CASCADE)
    price = models.FloatField(default=1.0)
    quantity = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return "{}: {} {}".format(self.id, self.food_tray, self.food)


class DeliveryAddress(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    address = models.CharField(max_length=200)
    state = models.CharField(max_length=100, blank=True, null=True)
    longitude = models.CharField(max_length=100, blank=True, null=True)
    latitude = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user}: {self.address}"

    class Meta:
        verbose_name_plural = "Delivery Addresses"


class CheckoutBill(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    food_tray = models.OneToOneField(FoodTray, on_delete=models.CASCADE)
    item_total = models.FloatField(default=1.0)
    discount = models.FloatField(default=0.0)
    delivery_fee = models.FloatField(default=1.0)
    service_fee = models.FloatField(default=1.0)
    total = models.FloatField(default=0.0)
    paid_at = models.DateTimeField(blank=True, null=True)
    payment_reference = models.CharField(max_length=300, blank=True, null=True)
    meta_data = models.TextField(blank=True, null=True)
    payment_mode = models.CharField(max_length=50, choices=PaymentModeChoices.choices, default="wallet")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.food_tray} - {self.total}"


class Order(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    payment = models.ForeignKey(CheckoutBill, on_delete=models.SET_NULL, blank=True, null=True)
    address = models.ForeignKey(DeliveryAddress, on_delete=models.SET_NULL, blank=True, null=True)
    status = models.CharField(max_length=50, choices=OrderStatusChoices.choices, default="processing")
    prep_instruction = models.TextField(blank=True, null=True)
    contact_phone = models.CharField(max_length=30)
    delivered_at = models.DateTimeField(blank=True, null=True)
    cancelled_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id}: {self.user}"


class OrderTracking(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    tracking_id = models.CharField(max_length=200, blank=True, null=True)
    rider = models.ForeignKey(DispatchDriver, on_delete=models.SET_NULL, blank=True, null=True)
    status = models.CharField(max_length=50, choices=OrderStatusChoices.choices, default="confirmed")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id} - {self.order.user.email}"






